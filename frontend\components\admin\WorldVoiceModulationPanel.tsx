/**
 * WORLD Vocoder Voice Modulation Panel for Admin
 * Manages 20+ predefined voice profiles with real-time preview
 * Supports custom profile creation and user assignment
 */

import React, { useState, useEffect, useRef } from 'react';
import Icon from './Icon';
import tokenManager from '../../utils/tokenManager';
import apiClient from '../../utils/apiClient';

export interface WorldVoiceProfile {
  name: string;
  description: string;
  parameters: {
    pitchScale: number;      // 0.7-1.3 (pitch modification)
    spectralWarp: number;    // -10% to +10% (formant shifting)
    reverbAmount: number;    // 0-50% (spatial distortion)
    eqTilt: number;         // -6dB to +6dB (frequency emphasis)
    temporalJitter: number; // Anti-forensic timing variation
    spectralNoise: number;  // Irreversible spectral masking
  };
  userType: 'all' | 'regular' | 'superuser';
  isCustom: boolean;
  audioSample?: string;    // Base64 encoded sample
}

interface WorldVoiceModulationPanelProps {
  userId: string;
  currentProfile: string;
  onProfileChange: (profileName: string) => void;
  isOpen: boolean;
  onClose: () => void;
}

const WorldVoiceModulationPanel: React.FC<WorldVoiceModulationPanelProps> = ({
  userId,
  currentProfile,
  onProfileChange,
  isOpen,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState<'profiles' | 'custom' | 'samples'>('profiles');
  const [profiles, setProfiles] = useState<WorldVoiceProfile[]>([]);
  const [selectedProfile, setSelectedProfile] = useState<WorldVoiceProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [worldVocoderEnabled, setWorldVocoderEnabled] = useState(false);
  const [userType, setUserType] = useState<'regular' | 'superuser'>('regular');
  
  // Custom profile creation state
  const [customProfile, setCustomProfile] = useState<WorldVoiceProfile>({
    name: '',
    description: '',
    parameters: {
      pitchScale: 1.0,
      spectralWarp: 0.0,
      reverbAmount: 0.0,
      eqTilt: 0.0,
      temporalJitter: 0.0,
      spectralNoise: 0.0
    },
    userType: 'all',
    isCustom: true
  });

  // Audio testing state
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentlyPlayingProfile, setCurrentlyPlayingProfile] = useState<string | null>(null);
  const [processingProfile, setProcessingProfile] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    if (isOpen) {
      fetchVoiceProfiles();
    }
  }, [isOpen]);

    const fetchVoiceProfiles = async () => {
    setIsLoading(true);
    try {
      const response = await apiClient.get('/api/voice/realtime/profiles');
      
      if (response.data.success) {
        setProfiles(response.data.profiles);
        setWorldVocoderEnabled(response.data.worldVocoderEnabled);
        setUserType(response.data.userType);
        
        // Find and set current profile
        const current = response.data.profiles.find((p: any) => p.name === currentProfile);
        if (current) {
          setSelectedProfile(current);
        }
      } else {
        console.error('Failed to fetch voice profiles:', response.data.error);
      }
    } catch (error) {
      console.error('Failed to fetch voice profiles:', error);
    } finally {
      setIsLoading(false);
    }
  };

    const handleProfileSelect = async (profile: any) => {
    setSelectedProfile(profile);
    onProfileChange(profile.name);
    
    // Update user's voice profile in backend
    try {
      await apiClient.put(`/api/admin/users/${userId}/voice-profile`, {
        profileName: profile.name
      });
    } catch (error) {
      console.error('Failed to update user voice profile:', error);
    }
  };

  const handleCustomProfileSave = async () => {
    if (!customProfile.name.trim()) {
      alert('Please enter a profile name');
      return;
    }

    setIsLoading(true);
    try {
      const response = await apiClient.post('/api/admin/voice/profiles/custom', customProfile);
      
      if (response.data.success) {
        await fetchVoiceProfiles();
        setActiveTab('profiles');
        // Reset custom profile form
        setCustomProfile({
          name: '',
          description: '',
          parameters: {
            pitchScale: 1.0,
            spectralWarp: 0.0,
            reverbAmount: 0.0,
            eqTilt: 0.0,
            temporalJitter: 0.0,
            spectralNoise: 0.0
          },
          userType: 'all',
          isCustom: true
        });
      }
    } catch (error) {
      console.error('Failed to save custom profile:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const playProfileSample = async (profile: WorldVoiceProfile) => {
    if (currentlyPlayingProfile === profile.name) {
      // Stop current playback
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      setIsPlaying(false);
      setCurrentlyPlayingProfile(null);
      return;
    }

    if (profile.audioSample) {
      // Play pre-recorded sample
      if (audioRef.current) {
        audioRef.current.src = `data:audio/wav;base64,${profile.audioSample}`;
        audioRef.current.play();
        setIsPlaying(true);
        setCurrentlyPlayingProfile(profile.name);
      }
    } else {
      // Generate sample using test audio
      await generateProfileSample(profile);
    }
  };

  const generateProfileSample = async (profile: WorldVoiceProfile) => {
    if (!audioFile) {
      setErrorMessage('Please upload a test audio file first');
      return;
    }

    setIsLoading(true);
    setProcessingProfile(profile.name);
    setErrorMessage(null);

    try {
      const formData = new FormData();
      formData.append('audio', audioFile);
      formData.append('profileName', profile.name);

      // Create AbortController for timeout handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await apiClient.post('/api/voice/realtime/test-profile', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        responseType: 'blob',
        signal: controller.signal,
        timeout: 30000
      });

      clearTimeout(timeoutId);

      if (response.data) {
        const audioBlob = response.data;
        const audioUrl = URL.createObjectURL(audioBlob);

        if (audioRef.current) {
          audioRef.current.src = audioUrl;
          audioRef.current.play();
          setIsPlaying(true);
          setCurrentlyPlayingProfile(profile.name);
        }
      }
    } catch (error: any) {
      console.error('Failed to generate profile sample:', error);

      let errorMsg = 'Failed to generate voice sample';
      if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {
        errorMsg = 'Voice processing timed out. Please try with a shorter audio file.';
      } else if (error.response?.data?.error) {
        errorMsg = error.response.data.error;
      } else if (error.message) {
        errorMsg = error.message;
      }

      setErrorMessage(errorMsg);
    } finally {
      setIsLoading(false);
      setProcessingProfile(null);
    }
  };

  const renderProfileCard = (profile: WorldVoiceProfile) => {
    const isSelected = selectedProfile?.name === profile.name;
    const isCurrentlyPlaying = currentlyPlayingProfile === profile.name && isPlaying;
    const isProcessing = processingProfile === profile.name;

    return (
      <div
        key={profile.name}
        className={`p-4 border rounded-lg cursor-pointer transition-all ${
          isSelected
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-200 hover:border-gray-300'
        } ${isProcessing ? 'opacity-75' : ''}`}
        onClick={() => handleProfileSelect(profile)}
      >
        <div className="flex justify-between items-start mb-2">
          <h3 className="font-medium text-gray-900">{profile.name}</h3>
          <div className="flex space-x-2">
            {profile.userType === 'regular' && (
              <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
                Regular Only
              </span>
            )}
            {profile.isCustom && (
              <span className="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded">
                Custom
              </span>
            )}
            <button
              onClick={(e) => {
                e.stopPropagation();
                playProfileSample(profile);
              }}
              className="p-1 text-gray-500 hover:text-blue-600 disabled:opacity-50"
              disabled={isLoading || isProcessing}
              title={isProcessing ? 'Processing...' : isCurrentlyPlaying ? 'Stop' : 'Play sample'}
            >
              {isProcessing ? (
                <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <Icon
                  name={isCurrentlyPlaying ? 'pause' : 'play'}
                  className="w-4 h-4"
                />
              )}
            </button>
          </div>
        </div>
        
        <p className="text-sm text-gray-600 mb-3">{profile.description}</p>
        
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div>
            <span className="text-gray-500">Pitch:</span> {profile.parameters.pitchScale}x
          </div>
          <div>
            <span className="text-gray-500">Spectral:</span> {profile.parameters.spectralWarp}%
          </div>
          <div>
            <span className="text-gray-500">Reverb:</span> {profile.parameters.reverbAmount}%
          </div>
          <div>
            <span className="text-gray-500">EQ Tilt:</span> {profile.parameters.eqTilt}dB
          </div>
        </div>
        
        {profile.parameters.temporalJitter > 0 && (
          <div className="mt-2 flex items-center text-xs text-orange-600">
            <Icon name="shield" className="w-3 h-3 mr-1" />
            Anti-forensic enabled
          </div>
        )}
      </div>
    );
  };

  const renderCustomProfileEditor = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Profile Name
        </label>
        <input
          type="text"
          value={customProfile.name}
          onChange={(e) => setCustomProfile(prev => ({ ...prev, name: e.target.value }))}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter profile name"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <textarea
          value={customProfile.description}
          onChange={(e) => setCustomProfile(prev => ({ ...prev, description: e.target.value }))}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={2}
          placeholder="Describe this voice profile"
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Pitch Scale ({customProfile.parameters.pitchScale}x)
          </label>
          <input
            type="range"
            min="0.7"
            max="1.3"
            step="0.05"
            value={customProfile.parameters.pitchScale}
            onChange={(e) => setCustomProfile(prev => ({
              ...prev,
              parameters: { ...prev.parameters, pitchScale: parseFloat(e.target.value) }
            }))}
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Spectral Warp ({customProfile.parameters.spectralWarp}%)
          </label>
          <input
            type="range"
            min="-10"
            max="10"
            step="0.5"
            value={customProfile.parameters.spectralWarp}
            onChange={(e) => setCustomProfile(prev => ({
              ...prev,
              parameters: { ...prev.parameters, spectralWarp: parseFloat(e.target.value) }
            }))}
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Reverb Amount ({customProfile.parameters.reverbAmount}%)
          </label>
          <input
            type="range"
            min="0"
            max="50"
            step="1"
            value={customProfile.parameters.reverbAmount}
            onChange={(e) => setCustomProfile(prev => ({
              ...prev,
              parameters: { ...prev.parameters, reverbAmount: parseFloat(e.target.value) }
            }))}
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            EQ Tilt ({customProfile.parameters.eqTilt}dB)
          </label>
          <input
            type="range"
            min="-6"
            max="6"
            step="0.5"
            value={customProfile.parameters.eqTilt}
            onChange={(e) => setCustomProfile(prev => ({
              ...prev,
              parameters: { ...prev.parameters, eqTilt: parseFloat(e.target.value) }
            }))}
            className="w-full"
          />
        </div>
      </div>

      <div className="border-t pt-4">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Security Features</h4>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Temporal Jitter ({customProfile.parameters.temporalJitter})
            </label>
            <input
              type="range"
              min="0"
              max="0.15"
              step="0.005"
              value={customProfile.parameters.temporalJitter}
              onChange={(e) => setCustomProfile(prev => ({
                ...prev,
                parameters: { ...prev.parameters, temporalJitter: parseFloat(e.target.value) }
              }))}
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Spectral Noise ({customProfile.parameters.spectralNoise})
            </label>
            <input
              type="range"
              min="0"
              max="0.35"
              step="0.01"
              value={customProfile.parameters.spectralNoise}
              onChange={(e) => setCustomProfile(prev => ({
                ...prev,
                parameters: { ...prev.parameters, spectralNoise: parseFloat(e.target.value) }
              }))}
              className="w-full"
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <button
          onClick={() => setActiveTab('profiles')}
          className="px-4 py-2 text-gray-600 hover:text-gray-800"
        >
          Cancel
        </button>
        <button
          onClick={handleCustomProfileSave}
          disabled={isLoading || !customProfile.name.trim()}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {isLoading ? 'Saving...' : 'Save Profile'}
        </button>
      </div>
    </div>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-end">
      <div className="w-96 bg-white h-full overflow-y-auto">
        <div className="p-4 border-b">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold">WORLD Voice Modulation</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <Icon name="close" className="w-5 h-5" />
            </button>
          </div>
          
          {!worldVocoderEnabled && (
            <div className="mt-2 p-2 bg-yellow-100 text-yellow-800 text-sm rounded">
              ⚠️ WORLD vocoder not available. Using fallback implementation.
            </div>
          )}
        </div>

        <div className="p-4">
          <div className="flex space-x-1 mb-4">
            {['profiles', 'custom', 'samples'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab as any)}
                className={`px-3 py-2 text-sm rounded-md ${
                  activeTab === tab
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </div>

          {activeTab === 'profiles' && (
            <div className="space-y-3">
              {isLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-600">Loading profiles...</p>
                </div>
              ) : (
                profiles.map(renderProfileCard)
              )}
            </div>
          )}

          {activeTab === 'custom' && renderCustomProfileEditor()}

          {activeTab === 'samples' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Upload Test Audio
                </label>
                <input
                  type="file"
                  accept="audio/*"
                  onChange={(e) => {
                    setAudioFile(e.target.files?.[0] || null);
                    setErrorMessage(null); // Clear error when new file is selected
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
                {audioFile && (
                  <div className="mt-2 flex items-center text-sm text-green-600">
                    <Icon name="check" className="w-4 h-4 mr-1" />
                    {audioFile.name} ({Math.round(audioFile.size / 1024)}KB)
                  </div>
                )}
              </div>

              {errorMessage && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <div className="flex items-center">
                    <Icon name="alert-circle" className="w-4 h-4 text-red-500 mr-2" />
                    <span className="text-sm text-red-700">{errorMessage}</span>
                  </div>
                </div>
              )}

              <p className="text-sm text-gray-600">
                Upload an audio file to test voice profiles. The file will be processed
                with each profile to generate preview samples. Supported formats: MP3, WAV, M4A.
              </p>
            </div>
          )}
        </div>

        <audio
          ref={audioRef}
          onEnded={() => {
            setIsPlaying(false);
            setCurrentlyPlayingProfile(null);
          }}
          onError={() => {
            setIsPlaying(false);
            setCurrentlyPlayingProfile(null);
          }}
        />
      </div>
    </div>
  );
};

export default WorldVoiceModulationPanel;
