/**
 * Real-time Voice Call API with WORLD Vocoder Integration
 * Handles voice call initiation, management, and real-time audio processing
 */

import { Router, Request, Response } from 'express';
import { authenticateToken } from '../../middleware/auth';
import { realTimeVoiceStreamingService } from '../../services/realTimeVoiceStreaming';
import { websocketService } from '../../services/websocket';
import { worldVocoderService, WORLD_VOICE_PROFILES } from '../../services/worldVocoderService';
import UserModel from '../../models/User';
import VoiceCallModel from '../../models/VoiceCall';
import AuditLogModel from '../../models/AuditLog';
import * as crypto from 'crypto';

const router = Router();

// Helper function to get authenticated user ID
function getAuthenticatedUserId(req: Request, res: Response): string | null {
  if (!(req as any).user || !(req as any).user.id) {
    res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
    return null;
  }
  return (req as any).user.id;
}

/**
 * Initiate a voice call with real-time voice morphing
 */
router.post('/initiate', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { recipientId, initiatorProfile, recipientProfile } = req.body;
    const initiatorId = getAuthenticatedUserId(req, res);
    if (!initiatorId) return; // Response already sent by helper

    // Validate users exist
    const [initiator, recipient] = await Promise.all([
      UserModel.findById(initiatorId),
      UserModel.findById(recipientId)
    ]);

    if (!initiator || !recipient) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Validate voice profiles
    const availableProfiles = Object.keys(WORLD_VOICE_PROFILES);
    if (!availableProfiles.includes(initiatorProfile) || !availableProfiles.includes(recipientProfile)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid voice profile'
      });
    }

    // Check profile restrictions (NORMAL_VOICE only for regular users)
    if (initiatorProfile === 'NORMAL_VOICE' && initiator.isSuperuser) {
      return res.status(403).json({
        success: false,
        error: 'NORMAL_VOICE profile not available for superusers'
      });
    }

    if (recipientProfile === 'NORMAL_VOICE' && recipient.isSuperuser) {
      return res.status(403).json({
        success: false,
        error: 'NORMAL_VOICE profile not available for superusers'
      });
    }

    // Initiate the call
    const callId = await realTimeVoiceStreamingService.initiateCall(
      initiatorId,
      recipientId,
      initiatorProfile,
      recipientProfile
    );

    // Create call record in database
    const voiceCall = new VoiceCallModel({
      callId,
      initiatorId,
      recipientId,
      status: 'initiating',
      startTime: new Date(),
      voiceProfiles: {
        initiator: initiatorProfile,
        recipient: recipientProfile
      },
      recordingEnabled: true,
      metadata: {
        worldVocoderEnabled: worldVocoderService.isAvailable(),
        realTimeProcessing: true,
        securityLevel: 'high'
      }
    });

    await voiceCall.save();

    // Send invitation to recipient via WebSocket
    websocketService.sendVoiceCallInvitation(callId, initiatorId, recipientId, {
      initiatorName: initiator.profile?.displayName || initiator.username,
      voiceProfile: recipientProfile
    });

    // Log the call initiation
    await AuditLogModel.create({
      action: 'voice_call_initiated',
      userId: initiatorId,
      targetUserId: recipientId,
      details: {
        callId,
        initiatorProfile,
        recipientProfile,
        worldVocoderEnabled: worldVocoderService.isAvailable()
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date()
    });

    res.json({
      success: true,
      callId,
      status: 'initiating',
      profiles: {
        initiator: initiatorProfile,
        recipient: recipientProfile
      },
      worldVocoderEnabled: worldVocoderService.isAvailable()
    });

  } catch (error) {
    console.error('Voice call initiation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to initiate voice call'
    });
  }
});

/**
 * Accept an incoming voice call
 */
router.post('/accept/:callId', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { callId } = req.params;
    const userId = getAuthenticatedUserId(req, res);
    if (!userId) return; // Response already sent by helper

    // Find the call
    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall) {
      return res.status(404).json({
        success: false,
        error: 'Call not found'
      });
    }

    // Verify user is the recipient
    if (voiceCall.recipientId !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Not authorized to accept this call'
      });
    }

    // Update call status
    voiceCall.status = 'ringing';
    await voiceCall.save();

    // Notify initiator that call was accepted
    if (voiceCall.initiatorId) {
      websocketService.broadcastVoiceCallEvent(callId, [voiceCall.initiatorId], {
        action: 'call_accepted',
        acceptedBy: userId
      });
    }

    res.json({
      success: true,
      callId,
      status: 'ringing',
      message: 'Call accepted, establishing connection...'
    });

  } catch (error) {
    console.error('Voice call accept error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to accept voice call'
    });
  }
});

/**
 * End a voice call
 */
router.post('/end/:callId', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { callId } = req.params;
    const userId = getAuthenticatedUserId(req, res);
    if (!userId) return; // Response already sent by helper

    // Find the call
    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall) {
      return res.status(404).json({
        success: false,
        error: 'Call not found'
      });
    }

    // Verify user is a participant
    if (voiceCall.initiatorId !== userId && voiceCall.recipientId !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Not authorized to end this call'
      });
    }

    // End the call in the streaming service
    await realTimeVoiceStreamingService.endCall(callId, userId);

    // Update call record
    voiceCall.status = 'ended';
    voiceCall.endTime = new Date();
    voiceCall.endedBy = userId;
    await voiceCall.save();

    // Notify other participant
    const otherParticipant = userId === voiceCall.initiatorId ? voiceCall.recipientId : voiceCall.initiatorId;
    if (otherParticipant) {
      websocketService.broadcastVoiceCallEvent(callId, [otherParticipant], {
        action: 'call_ended',
        endedBy: userId
      });
    }

    // Log the call end
    await AuditLogModel.create({
      action: 'voice_call_ended',
      userId,
      targetUserId: otherParticipant,
      details: {
        callId,
        duration: voiceCall.endTime.getTime() - voiceCall.startTime.getTime(),
        endedBy: userId
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date()
    });

    res.json({
      success: true,
      callId,
      status: 'ended',
      duration: voiceCall.endTime.getTime() - voiceCall.startTime.getTime()
    });

  } catch (error) {
    console.error('Voice call end error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to end voice call'
    });
  }
});

/**
 * Get call status and statistics
 */
router.get('/status/:callId', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { callId } = req.params;
    const userId = getAuthenticatedUserId(req, res);
    if (!userId) return; // Response already sent by helper

    // Find the call
    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall) {
      return res.status(404).json({
        success: false,
        error: 'Call not found'
      });
    }

    // Verify user is a participant
    if (voiceCall.initiatorId !== userId && voiceCall.recipientId !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Not authorized to view this call'
      });
    }

    // Get real-time statistics
    const stats = realTimeVoiceStreamingService.getCallStats(callId);

    res.json({
      success: true,
      callId,
      status: voiceCall.status,
      startTime: voiceCall.startTime,
      endTime: voiceCall.endTime,
      participants: {
        initiator: voiceCall.initiatorId,
        recipient: voiceCall.recipientId
      },
      voiceProfiles: voiceCall.voiceProfiles,
      recordingEnabled: voiceCall.metadata?.recordingEnabled ?? true,
      realTimeStats: stats,
      worldVocoderEnabled: worldVocoderService.isAvailable()
    });

  } catch (error) {
    console.error('Voice call status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get call status'
    });
  }
});

/**
 * Get available voice profiles for user
 */
router.get('/profiles', authenticateToken, async (req: Request, res: Response) => {
  try {
    // Check if user is authenticated
    const userId = getAuthenticatedUserId(req, res);
    if (!userId) return; // Response already sent by helper

    const user = await UserModel.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Get all available profiles
    const allProfiles = Object.keys(WORLD_VOICE_PROFILES);

    // Filter profiles based on user type
    const availableProfiles = allProfiles.filter(profileName => {
      // NORMAL_VOICE is only available for regular users
      if (profileName === 'NORMAL_VOICE') {
        return !user.isSuperuser;
      }
      return true;
    });

    // Get profile details
    const profileDetails = availableProfiles.map(profileName => ({
      name: profileName,
      description: getProfileDescription(profileName),
      parameters: WORLD_VOICE_PROFILES[profileName],
      userType: profileName === 'NORMAL_VOICE' ? 'regular_only' : 'all'
    }));

    res.json({
      success: true,
      profiles: profileDetails,
      worldVocoderEnabled: worldVocoderService.isNativeAvailable(),
      userType: user.isSuperuser ? 'superuser' : 'regular'
    });

  } catch (error) {
    console.error('Get voice profiles error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get voice profiles'
    });
  }
});

/**
 * Get profile description for display
 */
function getProfileDescription(profileName: string): string {
  const descriptions: Record<string, string> = {
    'SECURE_DEEP_MALE': 'Deep, masculine voice with security distortion',
    'SECURE_HIGH_FEMALE': 'Higher pitch, feminine voice with light distortion',
    'ROBOTIC_SYNTHETIC': 'Mechanical, robotic voice effect',
    'WHISPER_SOFT': 'Soft, whispered voice tone',
    'DRAMATIC_BASS': 'Very deep, dramatic bass voice',
    'ETHEREAL_HIGH': 'High-pitched, ethereal voice',
    'MECHANICAL_DRONE': 'Mechanical drone-like voice',
    'WARM_TENOR': 'Warm, tenor voice quality',
    'CRYSTAL_SOPRANO': 'Clear, high soprano voice',
    'DARK_BARITONE': 'Dark, rich baritone voice',
    'BRIGHT_ALTO': 'Bright, alto voice range',
    'MYSTERIOUS_ECHO': 'Mysterious voice with echo effects',
    'ENERGETIC_YOUNG': 'Energetic, youthful voice',
    'WISE_ELDER': 'Mature, wise voice tone',
    'DIGITAL_GLITCH': 'Digital, glitched voice effect',
    'SMOOTH_RADIO': 'Smooth, radio announcer voice',
    'INTENSE_GROWL': 'Intense, growling voice',
    'GENTLE_BREEZE': 'Gentle, soft voice like a breeze',
    'POWERFUL_BOOM': 'Powerful, booming voice',
    'SUBTLE_SHIFT': 'Subtle voice modification',
    'NORMAL_VOICE': 'Natural voice (regular users only)'
  };

  return descriptions[profileName] || 'Custom voice profile';
}

export default router;
